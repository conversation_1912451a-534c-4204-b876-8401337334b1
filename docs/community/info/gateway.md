# 交易接口

## 加载启动

### VeighNa Station加载

启动登录VeighNa Station后，点击【交易】按钮，在配置对话框中的【交易接口】栏勾选想要交易的接口。

### 脚本加载

以CTP接口为例，在启动脚本中添加如下代码：

```python3
# 写在顶部
from vnpy_ctp import CtpGateway

# 写在创建main_engine对象后
main_engine.add_gateway(CtpGateway)
```


## 连接接口

在图形化操作界面VeighNa Trader上的菜单栏中点击【系统】->【连接CTP】，会弹出账号配置窗口，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/gateway/1.png)

输入账号、密码等相关信息即可连接接口，并立刻进行查询工作: 如查询账号信息、查询持仓、查询委托信息、查询成交信息等。查询成功后可在主界面的组件中看到输出的日志，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/gateway/5.png)

### 修改json配置文件

接口配置相关信息保存在json文件中，放置在用户目录下的.vntrader文件夹内，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/gateway/3.png)

如果需要修改接口配置文件，用户既可以在图形化界面VeighNa Trader内修改，也可以直接在.vntrader文件夹下修改对应的json文件。

另外将json配置文件分离于vnpy的好处在于：避免每次升级都要重新配置json文件。

### 查看可交易的合约

先登录接口，然后在菜单栏中点击【帮助】->【查询合约】即可弹出空白的【查询合约】窗口，点击【查询】按钮后才会显示查询结果。留空则查询全部合约，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/gateway/4.png)


## 接口分类

| 接口                 |                    类型                         |
| ---------------------| :--------------------------------------------: |
| CTP                  |           期货、期货期权（实盘6.5.1）            |
| CTP测试              |           期货、期货期权（测试6.5.1）            |
| CTP Mini            |            期货、期货期权（实盘1.4）             |
| 飞马                 |                    期货                         |
| CTP期权              |             ETF期权（实盘20190802）              |
| 顶点飞创             |                    ETF期权                       |
| 顶点HTS              |                    ETF期权                       |
| 恒生UFT              |                期货、ETF期权                     |
| 易盛                 |                期货、黄金TD                      |
| 中泰XTP              |              A股、两融、ETF期权                  |
| 国泰君安统一交易网关   |                    A股                          |
| 华鑫奇点股票          |                    A股                          |
| 华鑫奇点期权          |                  ETF期权                        |
| 中亿汇达Comstar       |                银行间市场                       |
| 东方证券OST           |                    A股                          |
| 盈透证券              |                 海外多品种                      |
| 易盛9.0外盘           |                  外盘期货                       |
| 直达期货              |                  外盘期货                       |
| 融航                 |                  期货资管                        |
| TTS                  |                    期货                         |
| 飞鼠                  |                  黄金TD                         |
| 金仕达黄金            |                  黄金TD                         |


## 接口详解

### CTP

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - 期货
  - 期货期权

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 经纪商代码：
- 交易服务器：
- 行情服务器：
- 产品名称：
- 授权编码：
#### 获取账号

- 仿真账号：从SimNow网站上获取。只需输入手机号码和短信验证即可（短信验证有时只能在工作日正常工作时段收到）。SimNow的用户名(InvestorID)为6位纯数字，经纪商编号为9999，并且提供两套环境用于盘中仿真交易以及盘后的测试。需要修改一次密码之后才能使用。请注意每套仿真环境的适用时间段是不同的。
  
- 实盘账号：在期货公司开户，通过联系客户经理可以开通。用户名为纯数字，经纪商编号也是4位纯数字（每个期货公司的经纪商编号都不同）。另外，实盘账号也可以开通仿真交易功能，同样需要联系客户经理。

### CTPTEST（CTP测试）

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - 期货
  - 期货期权

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 经纪商代码：
- 交易服务器：
- 行情服务器：
- 产品名称：
- 授权编码：

#### 获取账号

在期货公司开户，通过联系客户经理向期货公司申请进行穿透式接入测试。

### MINI（CTP Mini）

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - 期货
  - 期货期权

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 经纪商代码：
- 交易服务器：
- 行情服务器：
- 产品名称：
- 授权编码：

#### 获取账号

在期货公司开户，通过联系客户经理可以开通。用户名为纯数字，经纪商编号也是4位纯数字（每个期货公司的经纪商编号都不同）。另外，实盘账号也可以开通仿真交易功能，同样需要联系客户经理。

### FEMAS（飞马）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - 期货

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 经纪商代码：
- 交易服务器：
- 行情服务器：
- 产品名称：
- 授权编码：

#### 获取账号

在期货公司开户，通过联系客户经理可以开通。用户名为纯数字，经纪商代码也是4位纯数字（每个期货公司的经纪商编号都不同）。另外，实盘账号也可以开通仿真交易功能，同样需要联系客户经理。

### SOPT（CTP期权）

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - ETF期权

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 经纪商代码：
- 交易服务器：
- 行情服务器：
- 产品名称：
- 授权编码：

#### 获取账号

在期货公司开户，通过联系客户经理可以开通。用户名为纯数字，经纪商代码也是4位纯数字（每个期货公司的经纪商代码都不同）。另外，实盘账号也可以开通仿真交易功能，同样需要联系客户经理。

### SEC（顶点飞创）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - ETF期权

- 持仓方向
  - 股票只支持单向持仓
  - 股票期权只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 账号：
- 密码：
- 行情地址：
- 交易地址：
- 行情协议：TCP、UDP
- 授权码：
- 产品号：
- 采集类型：顶点、恒生、金证、金仕达
- 行情压缩：N、Y

#### 获取账号

在期货公司开户，通过联系客户经理可以开通。

### HTS（顶点HTS）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - ETF期权

- 持仓方向
  - 双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 账号：
- 密码：
- 行情地址：
- 交易地址：
- 行情协议：TCP、UDP
- 授权码：
- 产品号：
- 采集类型：顶点、恒生、金证、金仕达
- 行情压缩：N、Y

#### 获取账号

在期货公司开户，通过联系客户经理可以开通。

### UFT（恒生UFT）

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - 期货
  - ETF期权

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 行情服务器：
- 交易服务器：
- 服务器类型：期货、ETF期权
- 产品名称：
- 授权编码：
- 委托类型：q

#### 获取账号

测试账号请通过恒生电子申请。

### ESUNNY（易盛）

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - 期货
  - 黄金TD

- 持仓方向
  - 支持双向持仓

- 历史数据
  - 不支持

#### 相关字段

- 行情账号：
- 行情密码：
- 行情服务器：
- 行情端口：0
- 行情授权码：
- 交易账号：
- 交易密码：
- 交易服务器：
- 交易端口：0
- 交易产品名称：
- 交易授权编码：
- 交易系统：内盘、外盘

#### 获取账号

测试账号请通过易盛官方网站申请。

### XTP（中泰柜台）

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - A股
  - 两融
  - ETF期权

- 持仓方向
  - 股票只支持单向持仓
  - 其余标的支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 账号：
- 密码：
- 客户号: 1
- 行情地址：
- 行情端口: 0
- 交易地址：
- 交易端口: 0
- 行情协议: TCP、UDP
- 日志级别：FATAL、ERROR、WARNING、INFO、DEBUG、TRACE
- 授权码：

#### 获取账号

测试账号请通过中泰证券申请。

#### 其他特点

XTP是首家提供融资融券的极速柜台。

### HFT（国泰君安统一交易网关）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - A股

- 持仓方向
  - 只支持单向持仓

- 历史数据
  - 不提供

#### 相关字段

- 交易用户名：
- 交易密码: 
- 交易服务器: 
- 交易端口：
- 机构代号：
- 营业部代号：
- 网关：
- 行情用户名：
- 行情密码：
- 行情服务器: 
- 行情端口：

#### 获取账号

测试账号请通过国泰君安申请。

### TORASTOCK（华鑫奇点股票）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - A股

- 持仓方向
  - 只支持单向持仓

- 历史数据
  - 不提供

#### 相关字段

- 账号：
- 密码：
- 行情服务器：
- 交易服务器：
- 账号类型：用户代码、资金账号
- 地址类型：前置地址、FENS地址

#### 获取账号

测试账号请通过华鑫证券申请。

### TORAOPTION（华鑫奇点期权）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - ETF期权

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 账号：
- 密码：
- 行情服务器：
- 交易服务器：
- 账号类型：用户代码、资金账号
- 地址类型：前置地址、FENS地址

#### 获取账号

测试账号请通过华鑫证券申请。

### COMSTAR（中亿汇达）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - 银行间市场

- 持仓方向
  - 无

- 历史数据
  - 不提供

#### 相关字段

- 交易服务器：
- 用户名：
- 密码：
- Key：
- routing_type：5
- valid_until_time：18:30:00.000

#### 获取账号

只有各类大型金融机构才能用（券商自营交易部、银行金融市场部等），私募或者个人都用不了。需要购买ComStar的交易接口服务之后才能使用。

### OST（东方证券）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - A股

- 持仓方向
  - 单向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 交易服务器：
- 上交所快照地址：
- 上交所快照端口: 0
- 深交所快照地址：
- 深交所快照端口: 0
- 本机ip地址：

#### 获取账号

在证券公司开户，通过联系客户经理可以开通。

### IB（盈透证券）

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu
  - Mac

- 交易品种
  - 海外多品种

- 持仓方向
  - 只支持单向持仓

- 历史数据
  - 提供

#### 相关字段

- TWS地址：127.0.0.1
- TWS端口：7497
- 客户号：1
- 交易账户：

#### 获取账号

在盈透证券开户并且入金后可以获得API接入权限。

#### 其他特点

可交易品种覆盖诸多海外市场的股票、期权、期权；手续费相对较低。

请注意，IB接口的合约代码较为特殊，请前往官网的产品查询板块查询。VeighNa Trader中使用的是盈透证券对于每个合约在某一交易所的唯一标识符ConId来作为合约代码，而非Symbol或者LocalName。

### TAP（易盛9.0外盘）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - 外盘期货

- 持仓方向
  - 只支持单向持仓

- 历史数据
  - 不提供

#### 相关字段

- 行情账号：
- 行情密码：
- 行情服务器：
- 行情端口：0
- 交易账号：
- 交易密码：
- 交易服务器：
- 交易端口：0
- 授权码：

#### 获取账号

测试账号请通过易盛官方网站申请。

### DA（直达期货）

#### 接口支持

- 操作系统
  - Windows

- 交易品种
  - 外盘期货

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 交易服务器：
- 行情服务器：
- 授权码：

#### 获取账号

在直达期货开户并且入金后可以获得API接入权限。

### ROHON（融航）

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - 期货资管

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 经纪商代码：
- 交易服务器：
- 行情服务器：
- 产品名称：
- 授权编码：

#### 获取账号

测试账号请通过融航申请。

请注意，融航接口的【经纪商代码】不再是纯数字形态，而是可以包含英文和数字的字符串；VeighNa连接融航进行交易在穿透式认证中属于【中继】模式，而不再是连接柜台（CTP、恒生等）进行交易时的【直连】模式，所以在申请穿透式认证测试填表时不要选错。

### TTS

#### 接口支持

- 操作系统
  - Windows
  - Ubuntu

- 交易品种
  - 期货
  - 期货期权

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 经纪商代码：
- 交易服务器：
- 行情服务器：
- 产品名称：
- 授权编码：

#### 获取账号

请通过OpenCTP平台获取。

### SGIT（飞鼠）

#### 接口支持

- 操作系统
  - Ubuntu

- 交易品种
  - 黄金TD

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 交易服务器：
- 行情服务器：
- 产品名称：
- 授权编码：

#### 获取账号

请通过黄金现货经纪商获取。

### KSGOLD（金仕达黄金）

#### 接口支持

- 操作系统
  - Ubuntu

- 交易品种
  - 黄金TD

- 持仓方向
  - 只支持双向持仓

- 历史数据
  - 不提供

#### 相关字段

- 用户名：
- 密码：
- 交易服务器：
- 行情服务器：
- 账号类型：银行账号、黄金账号

#### 获取账号

请通过黄金现货经纪商获取。
