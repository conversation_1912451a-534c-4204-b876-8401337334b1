{"cells": [{"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "from vnpy_portfoliostrategy import BacktestingEngine\n", "from vnpy.trader.constant import Interval\n", "from vnpy.trader.optimize import OptimizationSetting\n", "\n", "from vnpy_portfoliostrategy.strategies.pair_trading_strategy import PairTradingStrategy"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["engine = BacktestingEngine()\n", "engine.set_parameters(\n", "    vt_symbols=[\"y888.DCE\", \"p888.DCE\"],\n", "    interval=Interval.MINUTE,\n", "    start=datetime(2019, 1, 1),\n", "    end=datetime(2020, 4, 30),\n", "    rates={\n", "        \"y888.DCE\": 0/10000,\n", "        \"p888.DCE\": 0/10000\n", "    },\n", "    slippages={\n", "        \"y888.DCE\": 0,\n", "        \"p888.DCE\": 0\n", "    },\n", "    sizes={\n", "        \"y888.DCE\": 10,\n", "        \"p888.DCE\": 10\n", "    },\n", "    priceticks={\n", "        \"y888.DCE\": 1,\n", "        \"p888.DCE\": 1\n", "    },\n", "    capital=1_000_000,\n", ")\n", "\n", "setting = {\n", "    \"boll_window\": 20,\n", "    \"boll_dev\": 1,\n", "}\n", "engine.add_strategy(PairTradingStrategy, setting)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["engine.load_data()\n", "engine.run_backtesting()\n", "df = engine.calculate_result()\n", "engine.calculate_statistics()\n", "engine.show_chart()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["setting = OptimizationSetting()\n", "setting.set_target(\"sharpe_ratio\")\n", "setting.add_parameter(\"boll_window\", 10, 30, 1)\n", "setting.add_parameter(\"boll_dev\", 1, 3, 1)\n", "\n", "engine.run_ga_optimization(setting)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["engine.run_bf_optimization(setting)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}