{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from vnpy.trader.optimize import OptimizationSetting\n", "from vnpy_spreadtrading.backtesting import BacktestingEngine\n", "from vnpy_spreadtrading.strategies.statistical_arbitrage_strategy import (\n", "    StatisticalArbitrageStrategy\n", ")\n", "from vnpy_spreadtrading.base import LegData, SpreadData\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["spread = SpreadData(\n", "    name=\"IF-Spread\",\n", "    legs=[LegData(\"IF1911.CFFEX\"), LegData(\"IF1912.CFFEX\")],\n", "    variable_symbols={\"A\": \"IF1911.CFFEX\", \"B\": \"IF1912.CFFEX\"},\n", "    variable_directions={\"A\": 1, \"B\": -1},\n", "    price_formula=\"A-B\",\n", "    trading_multipliers={\"IF1911.CFFEX\": 1, \"IF1912.CFFEX\": 1},\n", "    active_symbol=\"IF1911.CFFEX\",\n", "    min_volume=1,\n", "    compile_formula=False                          # 回测时不编译公式，compile_formula传False，从而支持多进程优化\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["engine = BacktestingEngine()\n", "engine.set_parameters(\n", "    spread=spread,\n", "    interval=\"1m\",\n", "    start=datetime(2019, 6, 10),\n", "    end=datetime(2019, 11, 10),\n", "    rate=0,\n", "    slippage=0,\n", "    size=300,\n", "    pricetick=0.2,\n", "    capital=1_000_000,\n", ")\n", "engine.add_strategy(StatisticalArbitrageStrategy, {})"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["engine.load_data()\n", "engine.run_backtesting()\n", "df = engine.calculate_result()\n", "engine.calculate_statistics()\n", "engine.show_chart()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["for trade in engine.trades.values():\n", "    print(trade)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["setting = OptimizationSetting()\n", "setting.set_target(\"sharpe_ratio\")\n", "setting.add_parameter(\"boll_window\", 10, 30, 1)\n", "setting.add_parameter(\"boll_dev\", 1, 3, 1)\n", "\n", "engine.run_ga_optimization(setting)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["engine.run_bf_optimization(setting)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.1"}}, "nbformat": 4, "nbformat_minor": 2}